# DJ Music Organizer - Product Requirements Document

## Executive Summary

A desktop application that leverages AI to learn and replicate a DJ's unique music organization system, automating the time-intensive process of analyzing, categorizing, and organizing music files according to established patterns and preferences.

## Product Vision

Transform the manual DJ music preparation workflow into an intelligent, automated system that learns from existing organization patterns and applies that knowledge to new music, maintaining consistency while dramatically reducing preparation time.

## Background & Problem Statement

DJs spend significant time manually organizing music files, creating folder structures, and adding metadata tags that help with set preparation and track selection. The current manual process involves:
- Analyzing new tracks for sub-genre classification
- Determining appropriate folder placement based on energy levels and style
- Adding consistent metadata and comments
- Maintaining naming conventions across thousands of files

This process, while effective, is extremely time-consuming and prone to inconsistency as music libraries grow.

## Target User

Primary DJ who:
- Plays underground electronic music (Drum & Bass, House, Breaks, Techno)
- Has developed a sophisticated personal organization system
- Manages large music libraries (thousands of tracks)
- Values consistency and efficiency in music preparation
- Performs regular shows requiring quick track selection

## Core Product Requirements

### 1. Application Architecture

**Framework**: Tauri (Rust backend + React/TypeScript frontend)
- **Backend**: Rust for high-performance file operations and audio analysis
- **Frontend**: React with TypeScript for intuitive UI
- **Cross-platform**: Windows, macOS, Linux support
- **Offline-first**: No internet dependency for core functionality

### 2. Learn Mode - Pattern Recognition System

#### 2.1 Directory Analysis Engine
- **Smart Directory Traversal**: Recursively scan selected root directories
- **Pattern Recognition**: Identify organizational structures and naming conventions
- **Metadata Extraction**: Read ID3v2, FLAC, MP4, and other audio metadata formats
- **File Classification**: Detect audio formats and quality patterns

#### 2.2 Advanced Filtering System
**Inclusion/Exclusion Controls**:
- Directory-level toggles for selective analysis
- File type filtering (.mp3, .flac, .wav, .m4a, .aiff)
- Size-based filtering (minimum/maximum file sizes)
- Depth limiting for performance optimization

**Pattern Matching**:
- Text-based filters (contains, starts with, ends with, exact match)
- Regular expression support with validation
- Filter preset save/load functionality
- Real-time filter preview

#### 2.3 Music Organization Schema Learning
**Energy Level Classification**:
- Learn energy shorthand patterns: O, OM, M, ME, E, OPEN, END
- Understand combination patterns: [OPEN OM], [M ME]
- Map energy levels to folder structures and metadata

**Sub-genre Recognition**:
- Analyze folder structures: dark, ht, jungle, liquid, tf
- Learn placement rules based on track characteristics
- Detect naming conventions within each sub-genre

**Comment Field Pattern Analysis** (Critical Component):
- **Structure Recognition**: [vocal info]. [sub-genre]. [description/context]
- **Vocal Classification**: Learn patterns for vocal presence/absence notation
- **Sub-genre Consistency**: Verify comment sub-genre matches folder placement
- **Descriptive Elements**: Analyze patterns in musical element descriptions
  - Percussion-focused, arpeggio, heavy low-end, atmospheric
  - Emotional descriptors: dark, uplifting, melancholic, aggressive
  - Set context: sunrise set, peak hour, field party, tribal/forest vibes
- **Contextual Usage**: Learn when specific descriptors apply to track characteristics

**Metadata Pattern Analysis**:
- Key and BPM integration patterns in filenames
- Required field identification and validation
- Cross-reference comment patterns with audio analysis

#### 2.4 Audio Content Analysis & Learning Validation
**Audio Analysis**:
- **Tempo Detection**: BPM analysis and verification against filename/metadata
- **Key Detection**: Musical key identification and validation
- **Audio Fingerprinting**: Create unique track signatures for duplicate detection
- **Spectral Analysis**: Frequency distribution for sub-genre classification
- **Energy Level Detection**: Audio-based energy classification validation
- **Vocal Detection**: Automated vocal presence analysis to inform comment generation

**Machine Learning Validation System**:
- **Correction Interface**: Allow user to correct AI classifications in real-time
- **Feedback Loop**: Update classification models based on user corrections
- **Confidence Tracking**: Monitor and improve accuracy over time
- **Pattern Reinforcement**: Strengthen learned patterns through validation
- **Exception Handling**: Learn from edge cases and unusual classifications

#### 2.5 Rekordbox Integration & Playlist Analysis
**Rekordbox XML Analysis**:
- **XML Import**: Parse rekordbox.xml database file during Learn mode
- **Playlist Structure Mapping**: Analyze how Rekordbox playlists mirror file organization
- **Cue Point Analysis**: Extract and learn from existing cue points and hot cues
- **Rating/Color Correlation**: Map Rekordbox ratings and colors to organization patterns
- **Play Count Integration**: Factor track usage data into classification learning
- **Folder Hierarchy Mirroring**: Learn how file system organization translates to Rekordbox structure

**Cross-Reference Validation**:
- Compare file system organization with Rekordbox playlist structure
- Identify discrepancies and learn organizational preferences
- Validate comment field accuracy against Rekordbox metadata
- Learn playlist grouping strategies for different event types

### 3. Prep Mode - Automated Organization System

#### 3.1 Intelligent File Processing
- **Batch Processing**: Handle multiple new tracks simultaneously
- **Metadata Enhancement**: Auto-populate missing fields based on learned patterns
- **Quality Validation**: Verify audio file integrity and format consistency
- **Duplicate Detection**: Identify potential duplicate tracks using audio fingerprinting

#### 3.2 AI-Powered Classification with Approval Workflow
**Sub-genre Placement**:
- Analyze new tracks against learned patterns
- **Suggestion System**: Propose folder placement with confidence scoring
- **Approval Required**: All file moves require explicit user confirmation
- **Batch Approval**: Review and approve multiple suggestions simultaneously
- **Manual Override**: Easy correction and re-classification options

**Energy Level Assignment**:
- Determine appropriate energy shorthand based on audio analysis
- Cross-reference with learned comment patterns
- Account for combination classifications (e.g., [OPEN OM])
- **Preview Integration**: Show proposed energy level in suggested filename

**Comment Field Generation**:
- **Structured Generation**: Follow learned [vocal]. [sub-genre]. [description] pattern
- **Vocal Analysis**: Automated vocal detection to populate first field
- **Sub-genre Consistency**: Ensure comment sub-genre matches proposed folder
- **Contextual Description**: Generate musical element and vibe descriptions based on:
  - Audio analysis results (percussion patterns, frequency content)
  - Learned descriptive vocabulary from existing comments
  - Energy level and sub-genre context associations
- **Approval System**: Present generated comments for review and editing

#### 3.3 Organization Automation with User Control
**Suggested Operations Preview**:
- **Before/After Comparison**: Show current state vs. proposed organization
- **Comprehensive Preview**: Display proposed filename, folder location, and metadata changes
- **Confidence Indicators**: Visual representation of AI certainty for each suggestion
- **Selective Approval**: Approve individual tracks or bulk operations
- **Modification Interface**: Edit suggestions before approval

**File Operations** (Post-Approval):
- Automated file moving to suggested locations
- Batch renaming based on learned conventions
- Folder creation following established patterns
- **Comprehensive Undo**: Reverse any operation with full rollback capability

**Rekordbox Integration** (Prep Mode):
- **Playlist Generation**: Automatically create Rekordbox playlists based on organization
- **XML Update**: Modify rekordbox.xml to reflect new track organization
- **Cue Point Suggestions**: Propose standard cue points based on track analysis
- **Metadata Sync**: Ensure Rekordbox metadata matches file organization

### 4. User Interface Design

#### 4.1 Design System
**Color Scheme**:
- Primary: Dark theme (#1a1a1a background)
- Accent: Orange (#F77F00 or optimized variant)
- Secondary: Subtle grays for hierarchy
- Success/Error: Standard green/red with sufficient contrast

**Typography**:
- Clean, modern sans-serif font
- Clear hierarchy for headings and body text
- Monospace for file paths and technical data

#### 4.2 Layout Structure
**Learn Mode Interface**:
- Directory tree browser with expandable nodes
- **Rekordbox XML Import**: Dedicated section for rekordbox.xml file selection and analysis
- Filter panel with real-time preview
- **Comment Pattern Analyzer**: Dedicated view for comment field structure analysis
- **Validation Interface**: Correction tools for improving AI accuracy
- Analysis progress indicators with detailed status
- Schema preview and validation with export options

**Prep Mode Interface**:
- Drag-and-drop area for new music files
- **Suggestion Dashboard**: Classification results with confidence indicators and approval controls
- **Comment Preview**: Generated comment fields with editing capabilities
- **Batch Operations Panel**: Multi-track approval and modification interface
- **Rekordbox Integration**: Playlist generation and XML update controls
- Progress tracking for all operations

**Shared Components**:
- Navigation between modes
- Settings and configuration panel
- Progress indicators and status notifications
- Help and documentation access

### 5. Data Management & Export

#### 5.1 Schema Generation
**Output Formats**:
- JSON: Machine-readable configuration
- YAML: Human-readable documentation
- Custom format for application-specific use

**Schema Components**:
- Directory structure templates
- Naming convention rules with energy level integration
- **Comment Field Templates**: Structured patterns for [vocal]. [sub-genre]. [description]
- **Descriptive Vocabulary**: Learned terms for musical elements and contexts
- Energy level classification rules and combinations
- Sub-genre identification patterns
- **Rekordbox Mapping**: Playlist structure and organization translation rules

#### 5.2 Configuration Management
- **Profile System**: Multiple organization schemes
- **Import/Export**: Share configurations between installations
- **Version Control**: Track schema evolution
- **Backup/Restore**: Protect against data loss

### 6. Performance Requirements

#### 6.1 Scalability
- Handle libraries with 10,000+ tracks
- Process 100+ new tracks in single batch
- Maintain responsive UI during background operations
- Efficient memory usage for large directory scans

#### 6.2 Processing Speed
- Directory scan: <30 seconds for 5,000 tracks
- Single track analysis: <5 seconds
- Batch processing: 50+ tracks per minute
- Schema generation: <60 seconds for complete library

### 7. Technical Specifications

#### 7.1 Audio Format Support
**Primary Formats**:
- MP3 (all bitrates, CBR/VBR)
- FLAC (all compression levels)
- WAV (uncompressed)
- M4A/AAC (iTunes compatible)

**Extended Support**:
- AIFF (Apple format)
- OGG Vorbis
- WMA (Windows Media)

#### 7.2 Metadata Standards
- ID3v2.3 and ID3v2.4 (MP3)
- Vorbis Comments (FLAC, OGG)
- MP4 atoms (M4A)
- BWF metadata (WAV)

#### 7.3 System Requirements
**Minimum**:
- 4GB RAM
- 500MB storage
- Multi-core processor (2+ cores)
- Audio codec support

**Recommended**:
- 8GB+ RAM
- SSD storage
- Quad-core processor
- Dedicated audio interface

### 8. Security & Privacy

#### 8.1 Data Protection
- Local processing only (no cloud dependencies)
- User data remains on local machine
- Optional encrypted schema storage
- No telemetry or usage tracking

#### 8.2 File Safety
- Non-destructive analysis in Learn mode
- Confirmation required for all file operations
- Automatic backup creation before modifications
- Comprehensive undo functionality

### 9. Success Metrics

#### 9.1 Efficiency Gains
- 80%+ reduction in manual organization time
- 90%+ accuracy in sub-genre classification
- 95%+ consistency in naming conventions
- <5% manual correction rate

#### 9.2 User Experience
- Single-session learning for established libraries
- Intuitive operation without extensive training
- Clear visual feedback for all operations
- Minimal setup time for new users

### 10. Future Enhancements

#### 10.1 Advanced Features
- **Enhanced Rekordbox Integration**: Bi-directional sync, advanced cue point management
- Cloud sync for multi-device access
- Collaborative schema sharing within DJ communities
- **Advanced Audio Analysis**: Mood detection, instrument recognition, mix compatibility
- **Set Planning Tools**: Automated set list generation based on energy progression
- **Performance Analytics**: Track usage patterns and set optimization

#### 10.2 AI Improvements
- **Continuous Learning**: Real-time model updates from user corrections
- **Advanced Pattern Recognition**: Context-aware comment generation
- **Predictive Organization**: Anticipate organizational needs based on usage patterns
- **Community Learning**: Anonymous pattern sharing across user base (opt-in)
- **Natural Language Processing**: Enhanced comment analysis and generation

## Implementation Phases

### Phase 1: Core Architecture (Months 1-2)
- Tauri application framework setup
- Basic file system operations
- Initial UI components and routing
- Audio metadata reading capabilities

### Phase 2: Learn Mode Foundation (Months 2-3)
- Directory browsing and filtering
- Pattern recognition algorithms
- Schema generation system
- Basic metadata analysis

### Phase 3: AI Integration (Months 3-4)
- Audio content analysis
- Classification algorithms
- Learning system implementation
- Confidence scoring

### Phase 4: Prep Mode Development (Months 4-5)
- File processing pipeline
- Organization automation
- Preview and approval system
- Batch operations

### Phase 5: Polish & Optimization (Months 5-6)
- Performance optimization
- UI/UX refinement
- Comprehensive testing
- Documentation completion

## Risk Mitigation

### Technical Risks
- **Large file processing**: Implement streaming and chunked processing
- **Cross-platform compatibility**: Extensive testing on all target platforms
- **Audio codec support**: Comprehensive format testing and fallback options

### User Experience Risks
- **Complex setup**: Provide guided onboarding and sensible defaults
- **Incorrect classifications**: Robust correction mechanisms and learning
- **Data loss**: Multiple backup strategies and non-destructive operations

## Conclusion

This DJ Music Organizer will transform the music preparation workflow by learning from established organization patterns and applying that knowledge to automate the classification and organization of new music. The combination of advanced pattern recognition, audio analysis, and intuitive user interface will provide significant time savings while maintaining the consistency and precision required for professional DJ performance.